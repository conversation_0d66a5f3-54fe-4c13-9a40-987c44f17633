#include "show.h"
#include "bsp_oled.h"
#include "bsp_printf.h"  // 包含printf重定向头文件

//OLED接口
static pOLEDInterface_t oled = &UserOLED;
extern int xuanti;
//平衡任务的频率
extern uint8_t mainTaskFreq;

//平衡任务耗时
extern float mainTaskUseTime;
extern int i;
extern int openmv[10];
extern int x,y,state ;
extern	int16_t data1;
extern	int Encoder_Left,Encoder_Right,KEY0,HD0,HD1,HD2,HD3,HD4,HD5,HD6,HD7,gy,fx,time;
extern float m6050init;
int lfx=50;
void oled_show(void)
{
    // 发送分隔符，表示新的一帧数据开始
    printf("\r\n=== OLED Display Data ===\r\n");

    //第一行显示红外传感器状态
    oled->ShowString(0,0,"HD:");
    oled->ShowNumber(30,0,HD5,1,12);
    oled->ShowNumber(45,0,HD4,1,12);
	 oled->ShowNumber(60,0,HD3,1,12);
    oled->ShowNumber(75,0,HD2,1,12);
	oled->ShowNumber(90,0,HD1,1,12);
    oled->ShowNumber(105,0,HD0,1,12);

    // 串口输出红外传感器状态
    printf("HD: %d%d%d%d%d%d\r\n", HD5, HD4, HD3, HD2, HD1, HD0);

    //显示平衡任务的总体耗时,单位ms
//    oled->ShowFloat(80,0,mainTaskUseTime,2,2);

    //第二行显示角度
    oled->ShowString(0,10,"angle:");
    oled->ShowFloat(50,10,mpu6050.yaw-m6050init,3,2);
	oled->ShowFloat(100,10,time,3,2);

	// 串口输出角度信息
	printf("angle: %.2f  time: %.2f\r\n", mpu6050.yaw-m6050init, (float)time);

	if(mpu6050.yaw>(m6050init-45) && mpu6050.yaw<(m6050init+45))fx=50;
	if(mpu6050.yaw>(m6050init+45) && mpu6050.yaw<(m6050init+135))fx=51;
	if((mpu6050.yaw>(m6050init+135) && mpu6050.yaw<(m6050init+180)) || (mpu6050.yaw>(m6050init-180) && mpu6050.yaw<(m6050init-135)) )fx=52;
	if(mpu6050.yaw>(m6050init-135) && mpu6050.yaw<(m6050init-45))fx=53;
	if(fx!=lfx)DL_UART_transmitData(UART_2_INST,fx),lfx=fx;
	if(time>1050)time=1000,DL_UART_transmitData(UART_2_INST,fx),lfx=fx;

    //第三行显示fx和按键状态
    oled->ShowString(0,20,"fx:");
    oled->ShowFloat(5,20,fx,3,2);
		//第三行显示按键状态
    oled->ShowString(65,20,"k:");
	    oled->ShowFloat(70,20,KEY0,3,2);

	// 串口输出fx和按键状态
	printf("fx: %.2f  KEY0: %.2f\r\n", (float)fx, (float)KEY0);

    //第四、五行显示车轮信息
    oled->ShowString(0,30,"XT");
    if( Motor_Left < 0 )  oled->ShowString(16,30,"-");
    else oled->ShowString(16,30,"+");
    oled->ShowNumber(26,30,abs(xuanti),4,12);

    if( Velocity_Left < 0 )  oled->ShowString(60,30,"-");
    else oled->ShowString(60,30,"+");
    oled->ShowNumber(68,30,abs((int)Encoder_Left),4,12);
    oled->ShowString(96,30,"mm/s");

    // 串口输出循迹模式和左轮编码器
    printf("XT: %c%d  Left_Enc: %c%d mm/s\r\n",
           (Motor_Left < 0) ? '-' : '+', abs(xuanti),
           (Velocity_Left < 0) ? '-' : '+', abs((int)Encoder_Left));

    oled->ShowString(0,40,"gy");
    if( Velocity_Right < 0 )  oled->ShowString(16,40,"-");
    else oled->ShowString(16,40,"+");
    oled->ShowNumber(26,40,abs(gy),4,12);

    if( Velocity_Right < 0 )  oled->ShowString(60,40,"-");
    else oled->ShowString(60,40,"+");
    oled->ShowNumber(68,40,abs((int)Encoder_Right),4,12);
    oled->ShowString(96,40,"mm/s");

    // 串口输出圈数和右轮编码器
    printf("gy: %c%d  Right_Enc: %c%d mm/s\r\n",
           (Velocity_Right < 0) ? '-' : '+', abs(gy),
           (Velocity_Right < 0) ? '-' : '+', abs((int)Encoder_Right));

    //第六行显示电压、使能情况
    oled->ShowFloat(0,50,robotVol,2,2);
    oled->ShowString(50,50,"V");

    if( Flag_Stop )
        oled->ShowString(96,50,"OFF");
    else
        oled->ShowString(96,50," ON");

    // 串口输出电压和使能状态
    printf("Voltage: %.2fV  Status: %s\r\n", robotVol, Flag_Stop ? "OFF" : "ON");

    // 发送数据帧结束标志
    printf("=== End Frame ===\r\n\r\n");

    //刷新显示
    oled->RefreshGram();
}
