# OLED串口输出功能测试指南

## 测试准备

### 硬件需求
1. **TI MSPM0G3507开发板** (已有)
2. **串口转USB模块** (如CP2102、CH340等)
3. **杜邦线** 3根(TX、RX、GND)

### 软件需求
1. **串口调试助手** (推荐使用)
   - Windows: 串口调试助手、PuTTY、Tera Term
   - Linux/Mac: minicom、screen、CoolTerm
2. **Keil MDK** (编译下载程序)

## 硬件连接

### 引脚连接表
| 开发板引脚 | 串口模块引脚 | 说明 |
|------------|--------------|------|
| PA10 | RX | 开发板发送数据 |
| PA11 | TX | 开发板接收数据(可选) |
| GND | GND | 共地 |

### 连接示意图
```
MSPM0G3507开发板          串口转USB模块
    PA10 (TX) ---------> RX
    PA11 (RX) ---------> TX (可选)
    GND       ---------> GND
                            |
                            v
                        USB连接到PC
```

## 软件配置

### 1. 编译下载程序
1. 打开Keil工程文件
2. 编译项目 (Build -> Build Target)
3. 下载程序到开发板
4. 复位开发板启动程序

### 2. 串口调试助手设置
- **端口**: 根据设备管理器选择正确的COM口
- **波特率**: 9600
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控制**: 无

## 测试步骤

### 第一步：基础连接测试
1. 按照硬件连接图连接设备
2. 打开串口调试助手
3. 设置正确的串口参数
4. 点击"打开串口"
5. 复位开发板

**预期结果**: 应该看到类似以下的输出
```
=== OLED Display Data ===
HD: 000000
angle: 0.00  time: 1000.00
fx: 50.00  KEY0: 0.00
XT: +0  Left_Enc: +0 mm/s
gy: +0  Right_Enc: +0 mm/s
Voltage: 12.34V  Status: OFF
=== End Frame ===
```

### 第二步：传感器响应测试
1. 用手遮挡红外传感器HD0-HD5
2. 观察串口输出中HD字段的变化
3. 按下KEY0按键
4. 观察KEY0字段的变化

**预期结果**: 
- 遮挡传感器时，对应位置的数字应该从0变为1
- 按下按键时，KEY0字段应该有变化

### 第三步：运动传感器测试
1. 轻微倾斜开发板
2. 观察angle字段的变化
3. 如果有编码器连接，转动轮子
4. 观察Left_Enc和Right_Enc字段的变化

**预期结果**:
- 倾斜开发板时，angle值应该发生变化
- 转动编码器时，对应的编码器数值应该变化

### 第四步：数据完整性测试
1. 让程序运行5分钟
2. 观察数据输出是否稳定
3. 检查是否有数据丢失或格式错误
4. 记录数据更新频率

**预期结果**:
- 数据应该连续稳定输出
- 每秒约200帧数据(5ms一帧)
- 格式应该保持一致

## 测试验证标准

### 功能验证
- [ ] 串口能正常输出数据
- [ ] 数据格式正确，包含所有字段
- [ ] 红外传感器数据能正确反映
- [ ] 角度数据能正确反映MPU6050状态
- [ ] 编码器数据能正确反映(如果连接)
- [ ] 电压和状态信息正确显示

### 性能验证
- [ ] 数据输出频率约200Hz
- [ ] 无明显的数据丢失
- [ ] 系统运行稳定，无死机现象
- [ ] OLED显示正常，无影响

### 稳定性验证
- [ ] 连续运行30分钟无异常
- [ ] 多次复位后功能正常
- [ ] 不同环境温度下功能正常

## 常见问题及解决方案

### 问题1: 无串口输出
**可能原因**:
- 硬件连接错误
- 串口参数设置错误
- 程序未正常运行

**解决方案**:
1. 检查PA10是否正确连接到串口模块的RX
2. 确认串口参数：9600-8-N-1
3. 检查程序是否正常下载和运行
4. 使用万用表测试PA10是否有电平变化

### 问题2: 数据格式混乱
**可能原因**:
- 串口缓冲区溢出
- 波特率不匹配
- 数据传输中断

**解决方案**:
1. 重新设置串口参数
2. 清空串口缓冲区
3. 复位开发板重新开始

### 问题3: 数据更新缓慢
**可能原因**:
- printf函数执行时间过长
- 串口发送阻塞

**解决方案**:
1. 检查串口连接稳定性
2. 确认串口调试助手设置正确
3. 可以考虑降低输出频率

### 问题4: 部分数据显示异常
**可能原因**:
- 传感器未正确初始化
- 变量未正确定义
- 数据类型转换错误

**解决方案**:
1. 检查传感器初始化代码
2. 确认所有extern变量都已正确声明
3. 检查数据类型转换是否正确

## 测试记录表

### 基础功能测试
| 测试项目 | 预期结果 | 实际结果 | 状态 | 备注 |
|----------|----------|----------|------|------|
| 串口连接 | 有数据输出 |  | ⭕ |  |
| 数据格式 | 格式正确 |  | ⭕ |  |
| 红外传感器 | 响应正常 |  | ⭕ |  |
| 角度传感器 | 响应正常 |  | ⭕ |  |
| 编码器 | 响应正常 |  | ⭕ |  |
| 电压显示 | 数值合理 |  | ⭕ |  |

### 性能测试
| 测试项目 | 预期值 | 实际值 | 状态 | 备注 |
|----------|--------|--------|------|------|
| 输出频率 | ~200Hz |  | ⭕ |  |
| 数据完整性 | 无丢失 |  | ⭕ |  |
| 系统稳定性 | 稳定运行 |  | ⭕ |  |

### 测试结论
- 测试日期: ___________
- 测试人员: ___________
- 测试结果: ⭕通过 / ❌失败
- 问题记录: ___________
- 改进建议: ___________

---

**文档版本**: v1.0  
**创建日期**: 2025-01-31  
**负责人**: Alex (Engineer)
