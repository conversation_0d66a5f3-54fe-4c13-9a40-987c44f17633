./objects/bsp_systick.o: ..\Hardware\bsp_systick.c \
  ..\Hardware\bsp_systick.h ..\..\2025CODE727\ti_msp_dl_config.h \
  ..\source\ti\devices\msp\msp.h ..\source\ti\devices\DeviceFamily.h \
  ..\source\ti\devices\msp\m0p\mspm0g350x.h \
  ..\source\third_party\CMSIS\Core\Include\core_cm0plus.h \
  ..\source\ti\devices\msp\peripherals\hw_adc12.h \
  ..\source\ti\devices\msp\peripherals\hw_aes.h \
  ..\source\ti\devices\msp\peripherals\hw_comp.h \
  ..\source\ti\devices\msp\peripherals\hw_crc.h \
  ..\source\ti\devices\msp\peripherals\hw_dac12.h \
  ..\source\ti\devices\msp\peripherals\hw_dma.h \
  ..\source\ti\devices\msp\peripherals\hw_flashctl.h \
  ..\source\ti\devices\msp\peripherals\hw_gpio.h \
  ..\source\ti\devices\msp\peripherals\hw_gptimer.h \
  ..\source\ti\devices\msp\peripherals\hw_i2c.h \
  ..\source\ti\devices\msp\peripherals\hw_iomux.h \
  ..\source\ti\devices\msp\peripherals\hw_mathacl.h \
  ..\source\ti\devices\msp\peripherals\hw_mcan.h \
  ..\source\ti\devices\msp\peripherals\hw_oa.h \
  ..\source\ti\devices\msp\peripherals\hw_rtc.h \
  ..\source\ti\devices\msp\peripherals\hw_spi.h \
  ..\source\ti\devices\msp\peripherals\hw_trng.h \
  ..\source\ti\devices\msp\peripherals\hw_uart.h \
  ..\source\ti\devices\msp\peripherals\hw_vref.h \
  ..\source\ti\devices\msp\peripherals\hw_wuc.h \
  ..\source\ti\devices\msp\peripherals\hw_wwdt.h \
  ..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h \
  ..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h \
  ..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h \
  ..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h \
  ..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h \
  ..\source\ti\driverlib\driverlib.h ..\source\ti\driverlib\dl_adc12.h \
  ..\source\ti\driverlib\dl_common.h \
  ..\source\ti\driverlib\m0p\dl_factoryregion.h \
  ..\source\ti\driverlib\m0p\dl_core.h ..\source\ti\driverlib\dl_aes.h \
  ..\source\ti\driverlib\dl_aesadv.h ..\source\ti\driverlib\dl_comp.h \
  ..\source\ti\driverlib\dl_crc.h ..\source\ti\driverlib\dl_crcp.h \
  ..\source\ti\driverlib\dl_dac12.h ..\source\ti\driverlib\dl_dma.h \
  ..\source\ti\driverlib\dl_flashctl.h \
  ..\source\ti\driverlib\m0p\dl_sysctl.h \
  ..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h \
  ..\source\ti\driverlib\dl_gpamp.h ..\source\ti\driverlib\dl_gpio.h \
  ..\source\ti\driverlib\dl_i2c.h ..\source\ti\driverlib\dl_iwdt.h \
  ..\source\ti\driverlib\dl_lfss.h \
  ..\source\ti\driverlib\dl_keystorectl.h \
  ..\source\ti\driverlib\dl_lcd.h ..\source\ti\driverlib\dl_mathacl.h \
  ..\source\ti\driverlib\dl_mcan.h ..\source\ti\driverlib\dl_opa.h \
  ..\source\ti\driverlib\dl_rtc.h ..\source\ti\driverlib\dl_rtc_common.h \
  ..\source\ti\driverlib\dl_rtc_a.h \
  ..\source\ti\driverlib\dl_scratchpad.h ..\source\ti\driverlib\dl_spi.h \
  ..\source\ti\driverlib\dl_tamperio.h \
  ..\source\ti\driverlib\dl_timera.h ..\source\ti\driverlib\dl_timer.h \
  ..\source\ti\driverlib\dl_timerg.h ..\source\ti\driverlib\dl_trng.h \
  ..\source\ti\driverlib\dl_uart_extend.h \
  ..\source\ti\driverlib\dl_uart.h ..\source\ti\driverlib\dl_uart_main.h \
  ..\source\ti\driverlib\dl_vref.h ..\source\ti\driverlib\dl_wwdt.h \
  ..\source\ti\driverlib\m0p\dl_interrupt.h \
  ..\source\ti\driverlib\m0p\dl_systick.h
